import {
  CanActivate,
  ExecutionContext,
  Injectable,
  Logger,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { SocketClient } from '../interfaces/socket-client.interface';
import { WsException } from '@nestjs/websockets';
import { JwtUtilService, TokenType } from '@/modules/auth/guards/jwt.util';

@Injectable()
export class SocketAuthGuard implements CanActivate {
  private readonly logger = new Logger(SocketAuthGuard.name);

  constructor(
    private readonly jwtUtilService: JwtUtilService,
  ) {}

  canActivate(
    context: ExecutionContext,
  ): boolean | Promise<boolean> | Observable<boolean> {
    const client = context.switchToWs().getClient<SocketClient>();

    // Nếu đã xác thực trước đó
    if (client.user) {
      return true;
    }

    // Lấy token từ handshake
    const token = this.extractToken(client);
    if (!token) {
      this.logger.warn('Missing authentication token');
      throw new WsException('Unauthorized');
    }

    try {
      // Xác thực token sử dụng JwtUtilService
      const payload = this.jwtUtilService.verifyTokenWithType(
        token,
        TokenType.ACCESS,
      );

      // Lưu thông tin người dùng vào client
      client.user = payload;

      return true;
    } catch (error) {
      this.logger.error(`Authentication error: ${error.message}`);
      throw new WsException('Unauthorized');
    }
  }

  private extractToken(client: SocketClient): string | null {
    // Thử lấy token từ handshake auth
    const auth = client.handshake?.auth?.token;
    if (auth) {
      return auth;
    }

    // Thử lấy token từ handshake headers
    const headers = client.handshake?.headers?.authorization;
    if (headers && headers.startsWith('Bearer ')) {
      return headers.substring(7);
    }

    // Thử lấy token từ handshake query
    const query = client.handshake?.query?.token;
    if (query && typeof query === 'string') {
      return query;
    }

    return null;
  }
}
